# SuperChat Generator

A modern web application for generating YouTube SuperChat-style images with custom user icons, amounts, and messages.

## Features

### 🎨 **Modern User Interface**
- Clean, responsive design similar to the reference site
- Real-time color preview based on donation amount
- Drag-and-drop file upload with preview
- Character counters for input validation
- Toast notifications for user feedback

### 🚀 **Improved API**
- **Native Icon Processing**: Replaced third-party icon API with Sharp for better performance and reliability
- **Multiple Endpoints**: RESTful API with proper error handling
- **File Upload Support**: Direct image upload instead of URL-based icons
- **Enhanced Validation**: Comprehensive input validation and sanitization
- **Multiple Currency Support**: IDR, USD, and JPY currencies

### 🛠 **Technical Improvements**
- **Sharp Integration**: Native image processing for icons (resize, crop, optimize)
- **Security**: Added CORS, Helmet, and input validation
- **Error Handling**: Proper error responses and logging
- **Performance**: Optimized image generation pipeline
- **Compatibility**: Fixed Node.js version compatibility issues

## API Endpoints

### `POST /api/generate`
Generate SuperChat image with file upload
- **Body**: FormData with `name`, `money`, `text`, `currency`, and `icon` (file)
- **Response**: PNG image

### `GET /generate-image` (Legacy)
Generate SuperChat image with icon URL
- **Query**: `name`, `icon`, `money`, `text`
- **Response**: PNG image

### `GET /api/color-preview`
Get color scheme for donation amount
- **Query**: `money`
- **Response**: JSON with color information

### `GET /api/health`
Health check endpoint
- **Response**: JSON with status

## Installation & Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start the Server**
   ```bash
   npm start
   ```

3. **Access the Application**
   - Open your browser to `http://localhost:3000`
   - The modern UI will be available at the root path

## Usage

### Web Interface
1. Enter a username (max 50 characters)
2. Upload a profile icon (PNG, JPG, GIF up to 5MB)
3. Select currency and enter donation amount
4. Write your comment (max 500 characters)
5. Click "Generate SuperChat" to create the image
6. Download or copy the generated image

### API Usage
```javascript
// Using the new API with file upload
const formData = new FormData();
formData.append('name', 'Username');
formData.append('money', '50000');
formData.append('text', 'Hello World!');
formData.append('currency', 'IDR');
formData.append('icon', fileInput.files[0]);

fetch('/api/generate', {
    method: 'POST',
    body: formData
})
.then(response => response.blob())
.then(blob => {
    // Handle the generated image
});
```

## Color Tiers

The SuperChat colors are based on donation amounts (IDR):
- **Blue**: ≤ 19,999
- **Cyan**: 20,000 - 49,999
- **Green**: 50,000 - 99,999
- **Yellow**: 100,000 - 199,999
- **Orange**: 200,000 - 499,999
- **Magenta**: 500,000 - 999,999
- **Red**: ≥ 1,000,000

## Dependencies

- **express**: Web framework
- **canvas**: HTML5 Canvas API for Node.js
- **sharp**: High-performance image processing
- **multer**: File upload handling
- **cors**: Cross-origin resource sharing
- **helmet**: Security middleware
- **@twemoji/api**: Emoji rendering support

## File Structure

```
├── src/
│   └── index.js          # Main server file with improved API
├── public/
│   ├── index.html        # Modern web interface
│   ├── style.css         # Responsive styling
│   ├── script.js         # Client-side functionality
│   └── *.ttf            # Font files
├── package.json
└── README.md
```

## Improvements Made

1. **Replaced Third-party Icon API**: Now uses Sharp for native image processing
2. **Modern UI**: Created a responsive web interface similar to the reference
3. **Enhanced API**: Added proper validation, error handling, and multiple endpoints
4. **Security**: Added CORS, Helmet, and input sanitization
5. **File Upload**: Direct file upload instead of URL-based icons
6. **Multi-currency**: Support for IDR, USD, and JPY
7. **Better UX**: Real-time previews, drag-and-drop, and toast notifications

## License

ISC License
this API mimic youtube's superchat for indonesian rupiah
