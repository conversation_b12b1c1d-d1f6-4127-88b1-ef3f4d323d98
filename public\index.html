<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SuperChat Generator</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">SuperChat Generator</h1>
            <p class="subtitle">Create beautiful YouTube SuperChat images</p>
        </header>

        <div class="main-content">
            <div class="form-section">
                <form id="superchatForm" class="form">
                    <div class="form-group">
                        <label for="username" class="label">Username</label>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            class="input" 
                            placeholder="Enter username"
                            maxlength="50"
                            required
                        >
                        <div class="char-count">
                            <span id="usernameCount">0</span>/50
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="iconFile" class="label">Profile Icon</label>
                        <div class="file-upload-area" id="fileUploadArea">
                            <input 
                                type="file" 
                                id="iconFile" 
                                name="iconFile" 
                                class="file-input" 
                                accept="image/*"
                                required
                            >
                            <div class="file-upload-content">
                                <div class="upload-icon">📁</div>
                                <p class="upload-text">Click to upload or drag and drop</p>
                                <p class="upload-subtext">PNG, JPG, GIF up to 5MB</p>
                            </div>
                            <div class="file-preview" id="filePreview" style="display: none;">
                                <img id="previewImage" src="" alt="Preview">
                                <button type="button" class="remove-file" id="removeFile">×</button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="amount" class="label">Amount</label>
                        <div class="amount-input-group">
                            <select id="currency" name="currency" class="currency-select">
                                <option value="IDR">IDR (Rp)</option>
                                <option value="USD">USD ($)</option>
                                <option value="JPY">JPY (¥)</option>
                            </select>
                            <input 
                                type="number" 
                                id="amount" 
                                name="amount" 
                                class="input amount-input" 
                                placeholder="0"
                                min="0"
                                step="1"
                                required
                            >
                        </div>
                        <div class="color-preview" id="colorPreview">
                            <div class="color-bar" id="colorBar"></div>
                            <span class="color-text" id="colorText">Enter amount to see color</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="comment" class="label">Comment</label>
                        <textarea 
                            id="comment" 
                            name="comment" 
                            class="textarea" 
                            placeholder="Enter your SuperChat message"
                            maxlength="500"
                            rows="4"
                            required
                        ></textarea>
                        <div class="char-count">
                            <span id="commentCount">0</span>/500
                        </div>
                    </div>

                    <button type="submit" class="generate-btn" id="generateBtn">
                        <span class="btn-text">Generate SuperChat</span>
                        <div class="loading-spinner" id="loadingSpinner" style="display: none;"></div>
                    </button>
                </form>
            </div>

            <div class="preview-section">
                <div class="preview-container">
                    <h3 class="preview-title">Preview</h3>
                    <div class="preview-area" id="previewArea">
                        <div class="preview-placeholder">
                            <div class="placeholder-icon">🎬</div>
                            <p>Fill out the form to see preview</p>
                        </div>
                    </div>
                    <div class="preview-actions" id="previewActions" style="display: none;">
                        <button type="button" class="download-btn" id="downloadBtn">
                            Download Image
                        </button>
                        <button type="button" class="copy-btn" id="copyBtn">
                            Copy to Clipboard
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <footer class="footer">
            <p>&copy; 2024 SuperChat Generator. Made with ❤️</p>
        </footer>
    </div>

    <div class="toast" id="toast"></div>

    <script src="script.js"></script>
</body>
</html>
