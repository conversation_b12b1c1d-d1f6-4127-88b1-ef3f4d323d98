class SuperChatGenerator {
    constructor() {
        this.form = document.getElementById('superchatForm');
        this.fileInput = document.getElementById('iconFile');
        this.fileUploadArea = document.getElementById('fileUploadArea');
        this.filePreview = document.getElementById('filePreview');
        this.previewImage = document.getElementById('previewImage');
        this.removeFileBtn = document.getElementById('removeFile');
        this.generateBtn = document.getElementById('generateBtn');
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.previewArea = document.getElementById('previewArea');
        this.previewActions = document.getElementById('previewActions');
        this.downloadBtn = document.getElementById('downloadBtn');
        this.copyBtn = document.getElementById('copyBtn');
        this.toast = document.getElementById('toast');
        
        this.currentImageBlob = null;
        
        this.initializeEventListeners();
        this.initializeCharacterCounters();
        this.initializeFileUpload();
        this.initializeAmountPreview();
    }

    initializeEventListeners() {
        this.form.addEventListener('submit', this.handleFormSubmit.bind(this));
        this.removeFileBtn.addEventListener('click', this.removeFile.bind(this));
        this.downloadBtn.addEventListener('click', this.downloadImage.bind(this));
        this.copyBtn.addEventListener('click', this.copyToClipboard.bind(this));
    }

    initializeCharacterCounters() {
        const usernameInput = document.getElementById('username');
        const commentInput = document.getElementById('comment');
        const usernameCount = document.getElementById('usernameCount');
        const commentCount = document.getElementById('commentCount');

        usernameInput.addEventListener('input', () => {
            usernameCount.textContent = usernameInput.value.length;
        });

        commentInput.addEventListener('input', () => {
            commentCount.textContent = commentInput.value.length;
        });
    }

    initializeFileUpload() {
        this.fileUploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        this.fileUploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.fileUploadArea.addEventListener('drop', this.handleDrop.bind(this));
        this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
    }

    initializeAmountPreview() {
        const amountInput = document.getElementById('amount');
        amountInput.addEventListener('input', this.updateColorPreview.bind(this));
    }

    handleDragOver(e) {
        e.preventDefault();
        this.fileUploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.fileUploadArea.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.fileUploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.fileInput.files = files;
            this.handleFileSelect();
        }
    }

    handleFileSelect() {
        const file = this.fileInput.files[0];
        if (file) {
            if (file.size > 5 * 1024 * 1024) {
                this.showToast('File size must be less than 5MB', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                this.previewImage.src = e.target.result;
                this.filePreview.style.display = 'block';
                this.fileUploadArea.querySelector('.file-upload-content').style.display = 'none';
            };
            reader.readAsDataURL(file);
        }
    }

    removeFile() {
        this.fileInput.value = '';
        this.filePreview.style.display = 'none';
        this.fileUploadArea.querySelector('.file-upload-content').style.display = 'block';
    }

    async updateColorPreview() {
        const amount = document.getElementById('amount').value;
        const colorBar = document.getElementById('colorBar');
        const colorText = document.getElementById('colorText');

        if (!amount || amount <= 0) {
            colorBar.style.background = '#f3f4f6';
            colorText.textContent = 'Enter amount to see color';
            return;
        }

        try {
            const response = await fetch(`/api/color-preview?money=${amount}`);
            const data = await response.json();
            
            if (data.success) {
                const colors = data.data.colors;
                colorBar.style.background = `linear-gradient(135deg, ${colors[0]} 0%, ${colors[1]} 100%)`;
                
                const tier = this.getAmountTier(amount);
                colorText.textContent = `${tier} tier`;
            }
        } catch (error) {
            console.error('Error fetching color preview:', error);
        }
    }

    getAmountTier(amount) {
        amount = Number(amount);
        if (amount <= 19999) return 'Blue';
        if (amount <= 49999) return 'Cyan';
        if (amount <= 99999) return 'Green';
        if (amount <= 199999) return 'Yellow';
        if (amount <= 499999) return 'Orange';
        if (amount <= 999999) return 'Magenta';
        return 'Red';
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        if (!this.validateForm()) {
            return;
        }

        this.setLoading(true);

        try {
            const formData = new FormData();
            formData.append('name', document.getElementById('username').value.trim());
            formData.append('money', document.getElementById('amount').value);
            formData.append('text', document.getElementById('comment').value.trim());
            formData.append('currency', document.getElementById('currency').value);
            formData.append('icon', this.fileInput.files[0]);

            const response = await fetch('/api/generate', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const blob = await response.blob();
                this.currentImageBlob = blob;
                this.displayPreview(blob);
                this.showToast('SuperChat generated successfully!');
            } else {
                const errorData = await response.json();
                this.showToast(errorData.error || 'Failed to generate SuperChat', 'error');
            }
        } catch (error) {
            console.error('Error generating SuperChat:', error);
            this.showToast('Network error. Please try again.', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    validateForm() {
        const username = document.getElementById('username').value.trim();
        const amount = document.getElementById('amount').value;
        const comment = document.getElementById('comment').value.trim();
        const file = this.fileInput.files[0];

        if (!username) {
            this.showToast('Username is required', 'error');
            return false;
        }

        if (!amount || amount <= 0) {
            this.showToast('Valid amount is required', 'error');
            return false;
        }

        if (!comment) {
            this.showToast('Comment is required', 'error');
            return false;
        }

        if (!file) {
            this.showToast('Profile icon is required', 'error');
            return false;
        }

        return true;
    }

    setLoading(loading) {
        this.generateBtn.disabled = loading;
        this.loadingSpinner.style.display = loading ? 'block' : 'none';
        document.querySelector('.btn-text').textContent = loading ? 'Generating...' : 'Generate SuperChat';
    }

    displayPreview(blob) {
        const url = URL.createObjectURL(blob);
        this.previewArea.innerHTML = `<img src="${url}" alt="Generated SuperChat" class="preview-image">`;
        this.previewActions.style.display = 'flex';
    }

    downloadImage() {
        if (!this.currentImageBlob) return;

        const url = URL.createObjectURL(this.currentImageBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `superchat-${Date.now()}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showToast('Image downloaded successfully!');
    }

    async copyToClipboard() {
        if (!this.currentImageBlob) return;

        try {
            await navigator.clipboard.write([
                new ClipboardItem({
                    'image/png': this.currentImageBlob
                })
            ]);
            this.showToast('Image copied to clipboard!');
        } catch (error) {
            console.error('Error copying to clipboard:', error);
            this.showToast('Failed to copy to clipboard', 'error');
        }
    }

    showToast(message, type = 'success') {
        this.toast.textContent = message;
        this.toast.className = `toast ${type}`;
        this.toast.classList.add('show');
        
        setTimeout(() => {
            this.toast.classList.remove('show');
        }, 3000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SuperChatGenerator();
});
