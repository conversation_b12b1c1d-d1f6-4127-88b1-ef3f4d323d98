* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 400;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

.form-section, .preview-section {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 24px;
}

.label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 0.95rem;
}

.input, .textarea, .currency-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    font-family: inherit;
}

.input:focus, .textarea:focus, .currency-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.amount-input-group {
    display: flex;
    gap: 8px;
}

.currency-select {
    flex: 0 0 120px;
}

.amount-input {
    flex: 1;
}

.char-count {
    text-align: right;
    font-size: 0.85rem;
    color: #6b7280;
    margin-top: 4px;
}

.file-upload-area {
    position: relative;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #667eea;
    background-color: #f8faff;
}

.file-upload-area.dragover {
    border-color: #667eea;
    background-color: #f0f4ff;
}

.file-input {
    position: absolute;
    inset: 0;
    opacity: 0;
    cursor: pointer;
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: 8px;
}

.upload-text {
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
}

.upload-subtext {
    font-size: 0.85rem;
    color: #6b7280;
}

.file-preview {
    position: relative;
    display: inline-block;
}

.file-preview img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e5e7eb;
}

.remove-file {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ef4444;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.color-preview {
    margin-top: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.color-bar {
    width: 60px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
    background: #f3f4f6;
}

.color-text {
    font-size: 0.85rem;
    color: #6b7280;
}

.generate-btn {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.generate-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.preview-container {
    height: 100%;
}

.preview-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #374151;
}

.preview-area {
    min-height: 300px;
    border: 2px dashed #e5e7eb;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    background: #f9fafb;
}

.preview-placeholder {
    text-align: center;
    color: #6b7280;
}

.placeholder-icon {
    font-size: 3rem;
    margin-bottom: 12px;
}

.preview-image {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.preview-actions {
    display: flex;
    gap: 12px;
}

.download-btn, .copy-btn {
    flex: 1;
    padding: 12px;
    border: 2px solid #667eea;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.download-btn {
    background: #667eea;
    color: white;
}

.copy-btn {
    background: white;
    color: #667eea;
}

.download-btn:hover, .copy-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    font-size: 0.9rem;
}

.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 20px;
    background: #10b981;
    color: white;
    border-radius: 8px;
    font-weight: 500;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.toast.show {
    transform: translateX(0);
}

.toast.error {
    background: #ef4444;
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .form-section, .preview-section {
        padding: 20px;
    }
}
