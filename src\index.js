const express = require("express");
const { createCanvas, loadImage, registerFont } = require("canvas");
const fs = require("fs");
const path = require("path");
const { parse } = require("@twemoji/api");
const fetch = require("node-fetch");
const sharp = require("sharp");
const multer = require("multer");
const cors = require("cors");
const helmet = require("helmet");

const app = express();

// Security and CORS middleware
app.use(helmet({
  contentSecurityPolicy: false, // Disable for development
}));
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.static(path.join(__dirname, "..", "public")));

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  }
});

// Function to check if file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (err) {
    return false;
  }
}

// Register the fonts if they exist
const fontPaths = {
  regular: path.join(__dirname, "..", "public", "NotoSans-Regular.ttf"),
  emoji: path.join(__dirname, "..", "public", "NotoEmoji-Regular.ttf"),
};

if (fileExists(fontPaths.regular)) {
  registerFont(fontPaths.regular, { family: "NotoSans" });
} else {
  console.error(`Font file not found: ${fontPaths.regular}`);
}

if (fileExists(fontPaths.emoji)) {
  registerFont(fontPaths.emoji, { family: "NotoEmoji" });
} else {
  console.error(`Font file not found: ${fontPaths.emoji}`);
}

// Native icon processing function using Sharp with positioning and zoom
async function processIcon(iconBuffer, options = {}) {
  const {
    size = 40,
    zoom = 1,
    offsetX = 0,
    offsetY = 0
  } = options;

  try {
    // Calculate the source size based on zoom (higher zoom = smaller source area)
    const sourceSize = Math.round(size / zoom);

    // Get image metadata to calculate positioning
    const metadata = await sharp(iconBuffer).metadata();
    const { width, height } = metadata;

    // Calculate extract parameters for positioning
    const centerX = Math.round(width / 2);
    const centerY = Math.round(height / 2);
    const halfSource = Math.round(sourceSize / 2);

    // Apply offset (convert from -1 to 1 range to pixel offset)
    const pixelOffsetX = Math.round((offsetX * width) / 4);
    const pixelOffsetY = Math.round((offsetY * height) / 4);

    const left = Math.max(0, Math.min(width - sourceSize, centerX - halfSource + pixelOffsetX));
    const top = Math.max(0, Math.min(height - sourceSize, centerY - halfSource + pixelOffsetY));

    const processedIcon = await sharp(iconBuffer)
      .extract({
        left: left,
        top: top,
        width: Math.min(sourceSize, width - left),
        height: Math.min(sourceSize, height - top)
      })
      .resize(size, size, {
        fit: 'cover',
        position: 'center'
      })
      .png()
      .toBuffer();

    return processedIcon;
  } catch (error) {
    console.error('Error processing icon:', error);
    throw new Error('Failed to process icon');
  }
}

// Enhanced error handling
function sendError(res, message, statusCode = 400) {
  res.status(statusCode).json({
    success: false,
    error: message
  });
}

function sendSuccess(res, data, message = 'Success') {
  res.json({
    success: true,
    message: message,
    data: data
  });
}

// Enhanced validation function
function validateSuperchatData(data) {
  const { name, money, text } = data;
  const errors = [];

  if (!name || name.trim().length === 0) {
    errors.push('Name is required');
  }
  if (!money || isNaN(money) || Number(money) < 0) {
    errors.push('Valid money amount is required');
  }
  if (!text || text.trim().length === 0) {
    errors.push('Comment text is required');
  }
  if (name && name.length > 50) {
    errors.push('Name must be 50 characters or less');
  }
  if (text && text.length > 500) {
    errors.push('Comment must be 500 characters or less');
  }

  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

function color(money) {
  money = parseInt(money, 10);
  if (money <= 19999) {
    return [
      "#1565C0",
      "#1565C0",
      "rgba(255, 255, 255, 0.7)",
      "rgb(255, 255, 255)",
      "rgb(255, 255, 255)",
    ];
  } else if (money <= 49999) {
    return [
      "#00B8D4",
      "#00E5FF",
      "rgba(0, 0, 0, 0.7)",
      "rgb(0, 0, 0)",
      "rgb(0, 0, 0)",
    ];
  } else if (money <= 99999) {
    return [
      "#00BFA5",
      "#1DE9B6",
      "rgba(0, 0, 0, 0.7)",
      "rgb(0, 0, 0)",
      "rgb(0, 0, 0)",
    ];
  } else if (money <= 199999) {
    return [
      "#FFB300",
      "#FFCA28",
      "rgba(0, 0, 0, 0.7)",
      "rgb(0, 0, 0)",
      "rgb(0, 0, 0)",
    ];
  } else if (money <= 499999) {
    return [
      "#E65100",
      "#F57C00",
      "rgba(255, 255, 255, 0.7)",
      "rgb(255, 255, 255)",
      "rgb(255, 255, 255)",
    ];
  } else if (money <= 999999) {
    return [
      "#C2185B",
      "#E91E63",
      "rgba(255, 255, 255, 0.7)",
      "rgb(255, 255, 255)",
      "rgb(255, 255, 255)",
    ];
  } else {
    return [
      "#D00000",
      "#E62117",
      "rgba(255, 255, 255, 0.7)",
      "rgb(255, 255, 255)",
      "rgb(255, 255, 255)",
    ];
  }
}

async function wrapTextWithEmoji(ctx, text, x, y, maxWidth, lineHeight) {
  const words = text.split(" ");
  let line = "";
  let testLine = "";
  let testWidth = 0;
  for (let i = 0; i < words.length; i++) {
    const word = words[i];
    testLine = line + word + " ";
    testWidth = ctx.measureText(testLine).width;

    if (testWidth > maxWidth && i > 0) {
      await drawTextWithEmoji(ctx, line, x, y);
      line = word + " ";
      y += lineHeight;
    } else {
      line = testLine;
    }
  }
  await drawTextWithEmoji(ctx, line, x, y);
  return y + lineHeight;
}

// Function to draw circular icon
function drawCircularIcon(ctx, iconImage, x, y, size) {
  ctx.save();

  // Create circular clipping path
  ctx.beginPath();
  ctx.arc(x + size/2, y + size/2, size/2, 0, Math.PI * 2);
  ctx.clip();

  // Draw the icon
  ctx.drawImage(iconImage, x, y, size, size);

  ctx.restore();
}

// Main function to generate superchat image
async function generateSuperchatImage({ name, money, text, iconImage, currency = 'IDR' }) {
  const colors = color(money);
  const hexToRgb = (hex) => {
    const bigint = parseInt(hex.slice(1), 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;
    return [r, g, b];
  };
  const colorRgb = colors.map(hexToRgb);

  const width = 335;
  const maxWidth = 303;
  const lineHeight = 21;

  // Calculate text height using system fonts
  const tempCanvas = createCanvas(width, 1000);
  const tempCtx = tempCanvas.getContext("2d");
  tempCtx.font = '12px system-ui, -apple-system, "Segoe UI", Roboto, sans-serif';
  const textHeight = await wrapTextWithEmoji(
    tempCtx,
    text,
    0,
    0,
    maxWidth,
    lineHeight
  );
  const height = 56 + textHeight + 16;

  // Create main canvas
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext("2d");
  ctx.textDrawingMode = "glyph";

  // Draw background
  ctx.fillStyle = `rgb(${colorRgb[0][0]}, ${colorRgb[0][1]}, ${colorRgb[0][2]})`;
  ctx.fillRect(0, 0, width, 56);
  ctx.fillStyle = `rgb(${colorRgb[1][0]}, ${colorRgb[1][1]}, ${colorRgb[1][2]})`;
  ctx.fillRect(0, 56, width, height);

  // Draw name using system fonts
  ctx.font = 'bold 14px system-ui, -apple-system, "Segoe UI", Roboto, sans-serif';
  ctx.fillStyle = colors[2];
  await wrapTextWithEmoji(ctx, name, 72, 19, maxWidth, lineHeight);

  // Draw money amount using system fonts
  ctx.font = 'bold 16px system-ui, -apple-system, "Segoe UI", Roboto, sans-serif';
  ctx.fillStyle = colors[4];
  const currencySymbol = currency === 'IDR' ? 'Rp' : currency === 'USD' ? '$' : '¥';
  const formattedMoney = currency === 'IDR'
    ? Number(money).toLocaleString("id-ID")
    : Number(money).toLocaleString();
  ctx.fillText(`${currencySymbol}${formattedMoney}`, 72, 38);

  // Draw comment text using system fonts
  ctx.font = '12px system-ui, -apple-system, "Segoe UI", Roboto, sans-serif';
  ctx.fillStyle = colors[4];
  await wrapTextWithEmoji(ctx, text, 16, 77, maxWidth, lineHeight);

  // Draw circular icon
  drawCircularIcon(ctx, iconImage, 16, 8, 40);

  return canvas.toBuffer();
}

// Helper function to extract the src attribute from the img tag
function extractEmojiSrc(html) {
  const regex = /<img[^>]*src="([^"]+)"[^>]*>/;
  const match = html.match(regex);
  return match ? match[1] : null;
}

// Twemoji Image Function
function sanitizeText(text) {
  // Hilangkan karakter VARIATION SELECTOR-16 (U+FE0F)
  text = text.replace(/\uFE0F/g, "");

  // Normalisasi teks untuk menghilangkan karakter combining marks
  text = text.normalize("NFKD").replace(/[\u0300-\u036F]/g, "");

  // Hilangkan karakter Unicode khusus yang mungkin menyebabkan masalah
  text = text.replace(/[\u200B-\u200D\uFEFF]/g, ""); // Zero-width dan karakter spesial

  return text;
}

// Twemoji Image Function with system fonts
async function drawTextWithEmoji(ctx, text, x, y) {
  ctx.font = '12px system-ui, -apple-system, "Segoe UI", Roboto, sans-serif';
  const sanitizedText = sanitizeText(text);
  const words = sanitizedText.split(/(\s+)/); // Split by space while keeping the spaces
  for (const word of words) {
    if (!word.trim()) {
      x += ctx.measureText(word).width; // Add space width
      continue;
    }

    for (const char of word) {
      const emojiHtml = parse(char, { folder: "svg", ext: ".svg" });
      const emojiUrl = extractEmojiSrc(emojiHtml);

      if (emojiUrl) {
        try {
          const response = await fetch(emojiUrl);
          const buffer = await response.buffer();
          const image = await loadImage(buffer);

          ctx.drawImage(image, x, y - 12, 16, 16); // Adjust Y position and size of emoji
          x += 20; // Spacing between emojis
        } catch (error) {
          console.error(`Failed to load emoji: ${error.message}`);
        }
      } else {
        ctx.fillText(char, x, y);
        x += ctx.measureText(char).width; // Add width of character to X position
      }
    }
  }
}

// New API endpoint for generating superchat with file upload
app.post("/api/generate", upload.single('icon'), async (req, res) => {
  try {
    const {
      name,
      money,
      text,
      currency = 'IDR',
      iconZoom = 1,
      iconOffsetX = 0,
      iconOffsetY = 0
    } = req.body;

    // Validate input data
    const validation = validateSuperchatData({ name, money, text });
    if (!validation.isValid) {
      return sendError(res, validation.errors.join(', '), 400);
    }

    // Check if icon file is provided
    if (!req.file) {
      return sendError(res, 'Icon image is required', 400);
    }

    // Process the icon using Sharp with positioning options
    const iconOptions = {
      size: 40,
      zoom: Math.max(0.5, Math.min(3, Number(iconZoom) || 1)),
      offsetX: Math.max(-1, Math.min(1, Number(iconOffsetX) || 0)),
      offsetY: Math.max(-1, Math.min(1, Number(iconOffsetY) || 0))
    };

    const processedIconBuffer = await processIcon(req.file.buffer, iconOptions);
    const iconImage = await loadImage(processedIconBuffer);

    // Generate the superchat image
    const result = await generateSuperchatImage({
      name: name.trim(),
      money: Number(money),
      text: text.trim(),
      iconImage,
      currency
    });

    res.setHeader("Content-Type", "image/png");
    res.send(result);

  } catch (error) {
    console.error('Error generating superchat:', error);
    sendError(res, 'Failed to generate superchat image', 500);
  }
});

// Legacy endpoint for backward compatibility
app.get("/generate-image", async (req, res) => {
  try {
    const {
      name,
      icon,
      money,
      text,
      iconZoom = 1,
      iconOffsetX = 0,
      iconOffsetY = 0
    } = req.query;

    // Validate input data
    const validation = validateSuperchatData({ name, money, text });
    if (!validation.isValid) {
      return sendError(res, validation.errors.join(', '), 400);
    }

    if (!icon) {
      return sendError(res, 'Icon URL is required', 400);
    }

    // Fetch and process icon from URL
    const iconResponse = await fetch(icon);
    if (!iconResponse.ok) {
      return sendError(res, 'Failed to fetch icon from URL', 400);
    }

    const iconBuffer = await iconResponse.buffer();

    // Process the icon using Sharp with positioning options
    const iconOptions = {
      size: 40,
      zoom: Math.max(0.5, Math.min(3, Number(iconZoom) || 1)),
      offsetX: Math.max(-1, Math.min(1, Number(iconOffsetX) || 0)),
      offsetY: Math.max(-1, Math.min(1, Number(iconOffsetY) || 0))
    };

    const processedIconBuffer = await processIcon(iconBuffer, iconOptions);
    const iconImage = await loadImage(processedIconBuffer);

    // Generate the superchat image
    const result = await generateSuperchatImage({
      name: name.trim(),
      money: Number(money),
      text: text.trim(),
      iconImage,
      currency: 'IDR'
    });

    res.setHeader("Content-Type", "image/png");
    res.send(result);

  } catch (error) {
    console.error('Error generating superchat:', error);
    sendError(res, 'Failed to generate superchat image', 500);
  }
});

// API endpoint to get color preview
app.get("/api/color-preview", (req, res) => {
  const { money } = req.query;
  if (!money || isNaN(money)) {
    return sendError(res, 'Valid money amount is required');
  }

  const colors = color(Number(money));
  sendSuccess(res, {
    colors: colors,
    money: Number(money)
  });
});

// Health check endpoint
app.get("/api/health", (req, res) => {
  sendSuccess(res, { status: 'OK', timestamp: new Date().toISOString() });
});

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`SuperChat Generator API running on port ${PORT}`);
});

module.exports = app;
