const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');
const FormData = require('form-data');

// Test configuration
const API_BASE = 'http://localhost:3001';

async function testHealthEndpoint() {
    console.log('🔍 Testing health endpoint...');
    try {
        const response = await fetch(`${API_BASE}/api/health`);
        const data = await response.json();
        console.log('✅ Health check:', data);
        return true;
    } catch (error) {
        console.error('❌ Health check failed:', error.message);
        return false;
    }
}

async function testColorPreview() {
    console.log('🎨 Testing color preview endpoint...');
    try {
        const response = await fetch(`${API_BASE}/api/color-preview?money=50000`);
        const data = await response.json();
        console.log('✅ Color preview:', data);
        return true;
    } catch (error) {
        console.error('❌ Color preview failed:', error.message);
        return false;
    }
}

async function testLegacyEndpoint() {
    console.log('🔄 Testing legacy endpoint...');
    try {
        const testIconUrl = 'https://via.placeholder.com/40x40/FF0000/FFFFFF?text=T';
        const params = new URLSearchParams({
            name: 'Test User',
            icon: testIconUrl,
            money: '25000',
            text: 'This is a test message! 🎉'
        });
        
        const response = await fetch(`${API_BASE}/generate-image?${params}`);
        
        if (response.ok) {
            const buffer = await response.buffer();
            const outputPath = path.join(__dirname, 'test-output-legacy.png');
            fs.writeFileSync(outputPath, buffer);
            console.log('✅ Legacy endpoint test passed. Image saved to:', outputPath);
            return true;
        } else {
            const errorData = await response.json();
            console.error('❌ Legacy endpoint failed:', errorData);
            return false;
        }
    } catch (error) {
        console.error('❌ Legacy endpoint test failed:', error.message);
        return false;
    }
}

async function createTestIcon() {
    // Create a simple test icon using Canvas
    const { createCanvas } = require('canvas');
    const canvas = createCanvas(40, 40);
    const ctx = canvas.getContext('2d');
    
    // Draw a simple colored circle
    ctx.fillStyle = '#4285f4';
    ctx.fillRect(0, 0, 40, 40);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 20px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('T', 20, 28);
    
    return canvas.toBuffer();
}

async function testNewAPIEndpoint() {
    console.log('🚀 Testing new API endpoint with file upload...');
    try {
        // Create test icon
        const iconBuffer = await createTestIcon();
        
        // Create form data
        const formData = new FormData();
        formData.append('name', 'Test User');
        formData.append('money', '75000');
        formData.append('text', 'Hello from the new API! 🎊 This supports emojis and longer text.');
        formData.append('currency', 'IDR');
        formData.append('icon', iconBuffer, {
            filename: 'test-icon.png',
            contentType: 'image/png'
        });
        
        const response = await fetch(`${API_BASE}/api/generate`, {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const buffer = await response.buffer();
            const outputPath = path.join(__dirname, 'test-output-new.png');
            fs.writeFileSync(outputPath, buffer);
            console.log('✅ New API endpoint test passed. Image saved to:', outputPath);
            return true;
        } else {
            const errorData = await response.json();
            console.error('❌ New API endpoint failed:', errorData);
            return false;
        }
    } catch (error) {
        console.error('❌ New API endpoint test failed:', error.message);
        return false;
    }
}

async function testValidation() {
    console.log('🛡️ Testing input validation...');
    try {
        const formData = new FormData();
        formData.append('name', ''); // Empty name should fail
        formData.append('money', '-100'); // Negative money should fail
        formData.append('text', '');
        formData.append('currency', 'IDR');
        
        const response = await fetch(`${API_BASE}/api/generate`, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            console.log('✅ Validation working correctly:', errorData.error);
            return true;
        } else {
            console.error('❌ Validation failed - should have rejected invalid input');
            return false;
        }
    } catch (error) {
        console.error('❌ Validation test failed:', error.message);
        return false;
    }
}

async function runAllTests() {
    console.log('🧪 Starting SuperChat Generator API Tests\n');
    
    const tests = [
        { name: 'Health Check', fn: testHealthEndpoint },
        { name: 'Color Preview', fn: testColorPreview },
        { name: 'Input Validation', fn: testValidation },
        { name: 'Legacy Endpoint', fn: testLegacyEndpoint },
        { name: 'New API Endpoint', fn: testNewAPIEndpoint }
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
        console.log(`\n--- ${test.name} ---`);
        const result = await test.fn();
        if (result) {
            passed++;
        } else {
            failed++;
        }
        
        // Wait a bit between tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('\n📊 Test Results:');
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
        console.log('\n🎉 All tests passed! The API is working correctly.');
    } else {
        console.log('\n⚠️ Some tests failed. Please check the server logs.');
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { runAllTests };
